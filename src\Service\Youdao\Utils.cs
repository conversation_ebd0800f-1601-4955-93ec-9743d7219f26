namespace Translator.Service.Youdao.Utils;

public class YoudaoUtils
{
    Middleware.LanguageCodeHelper.DiffCultureLanguageCodeHelper youdaoLanguageCodeHelper = new(
        new Dictionary<string, string>{
            { "zh-<PERSON>", "zh-CH<PERSON>" },
            { "zh-<PERSON><PERSON>", "zh-CHT" },
            { "zh-C<PERSON>", "zh-CHS" },
            { "zh-TW", "zh-CHT" },
            { "zh-SG", "zh-CHS" },
            {"zh", "zh-CHS"}
        }
    );


    public string GetYoudaoLanguageCode(string curtualName)
    {
        return youdaoLanguageCodeHelper.GetLanguageCode(curtualName);
    }

    private static YoudaoUtils? __instance;
    public static YoudaoUtils Instance
    {
        get
        {
            __instance ??= new YoudaoUtils();
            return __instance;
        }
    }
}