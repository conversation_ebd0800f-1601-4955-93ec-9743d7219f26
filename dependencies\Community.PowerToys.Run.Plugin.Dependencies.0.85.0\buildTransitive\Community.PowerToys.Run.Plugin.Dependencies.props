<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Condition="'$(Platform)' == 'ARM64'">
    <Reference Include="PowerToys.Common.UI.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\ARM64\PowerToys.Common.UI.dll" />
    <Reference Include="PowerToys.ManagedCommon.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\ARM64\PowerToys.ManagedCommon.dll" />
    <Reference Include="PowerToys.Settings.UI.Lib.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\ARM64\PowerToys.Settings.UI.Lib.dll" />
    <Reference Include="Wox.Infrastructure.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\ARM64\Wox.Infrastructure.dll" />
    <Reference Include="Wox.Plugin.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\ARM64\Wox.Plugin.dll" />
  </ItemGroup>
  <ItemGroup Condition="'$(Platform)' == 'x64'">
    <Reference Include="PowerToys.Common.UI.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\x64\PowerToys.Common.UI.dll" />
    <Reference Include="PowerToys.ManagedCommon.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\x64\PowerToys.ManagedCommon.dll" />
    <Reference Include="PowerToys.Settings.UI.Lib.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\x64\PowerToys.Settings.UI.Lib.dll" />
    <Reference Include="Wox.Infrastructure.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\x64\Wox.Infrastructure.dll" />
    <Reference Include="Wox.Plugin.dll" HintPath="$(MSBuildThisFileDirectory)..\tools\x64\Wox.Plugin.dll" />
  </ItemGroup>
</Project>
