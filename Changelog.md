# Changelog

## v0.4

- add
  - 新增了搜索建议
- fix
  - 修复了之前搜索的卡顿问题
  - 修复了无法在中间输入的问题

## v0.5 2023/2/14

- add
  - 新增了翻译 api, 整体迁移到新 api
  - 新增了出现错误寻求帮助的快捷方式
- fix
  - 修改了初始化逻辑

## v0.5.1 2023/2/15

- add
  - 新增了备用接口, 增加了稳定性

## v0.6 2023/3/9

- add
  - 新增朗读功能, 支持中英文朗读, 快捷键 ctrl+enter
  - 新增自动朗读结果(默认关闭)
- change
  - 项目名称更名为`PowerTranslator`

## v0.7 2023/4/10

- add
  - 新增历史记录功能, 键入`h`查看历史翻译
- fix
  - 修复了翻译被特殊字符截断的问题
  - 优化了初始化速度

## v0.7.1 2023/5/13

- fix
  - 修复了历史记录功能会错误地将空查询记录
  - 优化了初始化速度, 此前的版本初始化速度为 **6020ms**, 优化后为 **3ms**.(其实总的初始化时间是不会改变的, 只是将比较耗时的部分移动到了后台执行)

## v0.8 2023/9/21

- feat
  - 改进了信息的展示
  - 改进了初始化过程
- fix
  - 修复了接口失效问题
  - 修复了一个接口出现问题后的接口切换错误

## v0.9 2023/10/1

- feat
  - 新增了默认目标语言选项

## v0.10 2024/1/14

- feat
  - 新增第二语言选项
  - 为单个单词添加音标显示
  

## v0.10.1 2024/1/20

- fix
  - 修复了复制时默认带上音标的错误
  - 内地接口之后不再使用系统默认代理

## v0.11.0 2024/5/12

- feat
  - 新增打开词典快捷方式，自带 有道、牛津、剑桥 词典快捷方式
  - 新增若干可选语言
